/**
 * Utility functions for avatar generation and handling
 */

/**
 * Generate a temporary avatar URL based on user's name
 * Uses a service like DiceBear or UI Avatars for consistent placeholder avatars
 */
export function generateTempAvatar(name: string, size: number = 40): string {
  if (!name) return generateDefaultAvatar(size)
  
  // Clean the name for URL usage
  const cleanName = encodeURIComponent(name.trim())
  
  // Use UI Avatars service for consistent, colorful avatars
  return `https://ui-avatars.com/api/?name=${cleanName}&size=${size}&background=00796B&color=fff&bold=true&format=svg`
}

/**
 * Generate a default avatar for users without names
 */
export function generateDefaultAvatar(size: number = 40): string {
  return `https://ui-avatars.com/api/?name=User&size=${size}&background=9CA3AF&color=fff&bold=true&format=svg`
}

/**
 * Get the best available avatar URL with fallbacks
 */
export function getBestAvatar(
  photoURL?: string | null,
  name?: string | null,
  size: number = 40
): string {
  if (photoURL) return photoURL
  if (name) return generateTempAvatar(name, size)
  return generateDefaultAvatar(size)
}

/**
 * Get initials from a name for avatar fallback
 */
export function getInitials(name?: string | null): string {
  if (!name) return "U"
  
  const words = name.trim().split(/\s+/)
  if (words.length === 1) {
    return words[0].charAt(0).toUpperCase()
  }
  
  return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase()
}
