import { db } from "@/lib/firebase"
import {
  collection,
  query,
  orderBy,
  limit,
  startAfter,
  onSnapshot,
  DocumentSnapshot,
} from "firebase/firestore"
import { BaseRealtimeService } from "../base/base.realtime.service"
import { Message } from "./message.types"

/**
 * Message real-time service for Firebase operations
 */
export class MessageRealtimeService extends BaseRealtimeService {
  private static readonly COLLECTION = "trips"
  private static readonly SUBCOLLECTION = "messages"

  /**
   * Subscribe to messages for a trip with real-time updates
   * @param tripId Trip ID
   * @param limitCount Number of messages to fetch
   * @param lastDoc Last document for pagination
   * @param callback Callback function to handle message updates
   * @returns Unsubscribe function
   */
  static subscribeToMessages(
    tripId: string,
    limitCount: number = 20,
    lastDoc?: DocumentSnapshot,
    callback: (data: { messages: Message[]; lastDoc: DocumentSnapshot | null }, error?: Error) => void
  ): () => void {
    try {
      let q = query(
        collection(db, this.COLLECTION, tripId, this.SUBCOLLECTION),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      )

      if (lastDoc) {
        q = query(q, startAfter(lastDoc))
      }

      return onSnapshot(
        q,
        (snapshot) => {
          try {
            const messages: Message[] = []
            let newLastDoc: DocumentSnapshot | null = null

            snapshot.forEach((doc) => {
              const data = doc.data()
              messages.push({
                id: doc.id,
                ...data,
              } as Message)
            })

            if (snapshot.docs.length > 0) {
              newLastDoc = snapshot.docs[snapshot.docs.length - 1]
            }

            callback({ messages, lastDoc: newLastDoc })
          } catch (error) {
            console.error("Error processing message snapshot:", error)
            callback({ messages: [], lastDoc: null }, error as Error)
          }
        },
        (error) => {
          console.error("Error in message subscription:", error)
          callback({ messages: [], lastDoc: null }, error)
        }
      )
    } catch (error) {
      console.error("Error setting up message subscription:", error)
      callback({ messages: [], lastDoc: null }, error as Error)
      return () => {}
    }
  }

  /**
   * Subscribe to latest messages for a trip (for new message notifications)
   * @param tripId Trip ID
   * @param callback Callback function to handle new messages
   * @returns Unsubscribe function
   */
  static subscribeToLatestMessages(
    tripId: string,
    callback: (messages: Message[], error?: Error) => void
  ): () => void {
    try {
      const q = query(
        collection(db, this.COLLECTION, tripId, this.SUBCOLLECTION),
        orderBy("createdAt", "desc"),
        limit(1)
      )

      return onSnapshot(
        q,
        (snapshot) => {
          try {
            const messages: Message[] = []

            snapshot.forEach((doc) => {
              const data = doc.data()
              messages.push({
                id: doc.id,
                ...data,
              } as Message)
            })

            callback(messages)
          } catch (error) {
            console.error("Error processing latest message snapshot:", error)
            callback([], error as Error)
          }
        },
        (error) => {
          console.error("Error in latest message subscription:", error)
          callback([], error)
        }
      )
    } catch (error) {
      console.error("Error setting up latest message subscription:", error)
      callback([], error as Error)
      return () => {}
    }
  }
}
