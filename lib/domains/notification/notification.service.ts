import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  setDoc,
  deleteDoc,
  writeBatch,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { Notification, NotificationCreateData, NotificationUpdateData } from "./notification.types"

/**
 * Notification service for Firebase operations
 */
export class NotificationService {
  private static readonly COLLECTION = "users"
  private static readonly SUBCOLLECTION = "notifications"

  /**
   * Create a new notification
   * @param userId User ID
   * @param notificationData Notification data
   * @returns The new notification ID
   */
  static async createNotification(
    userId: string,
    notificationData: NotificationCreateData
  ): Promise<string> {
    try {
      const notificationRef = doc(collection(db, this.COLLECTION, userId, this.SUBCOLLECTION))
      const notificationId = notificationRef.id

      await setDoc(notificationRef, {
        ...notificationData,
        id: notificationId,
        createdAt: serverTimestamp(),
      })

      return notificationId
    } catch (error) {
      console.error("Error creating notification:", error)
      throw error
    }
  }

  /**
   * Create multiple notifications for message mentions
   * @param mentionedUserIds Array of user IDs to notify
   * @param notificationData Base notification data
   * @returns Array of created notification IDs
   */
  static async createMessageMentionNotifications(
    mentionedUserIds: string[],
    notificationData: Omit<NotificationCreateData, "userId">
  ): Promise<string[]> {
    try {
      const batch = writeBatch(db)
      const notificationIds: string[] = []

      for (const userId of mentionedUserIds) {
        const notificationRef = doc(collection(db, this.COLLECTION, userId, this.SUBCOLLECTION))
        const notificationId = notificationRef.id

        batch.set(notificationRef, {
          ...notificationData,
          userId,
          id: notificationId,
          createdAt: serverTimestamp(),
        })

        notificationIds.push(notificationId)
      }

      await batch.commit()
      return notificationIds
    } catch (error) {
      console.error("Error creating message mention notifications:", error)
      throw error
    }
  }

  /**
   * Get notifications for a user
   * @param userId User ID
   * @param limitCount Number of notifications to fetch
   * @returns Array of notifications
   */
  static async getNotifications(userId: string, limitCount: number = 50): Promise<Notification[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION, userId, this.SUBCOLLECTION),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      )

      const snapshot = await getDocs(q)
      const notifications: Notification[] = []

      snapshot.forEach((doc) => {
        const data = doc.data()
        notifications.push({
          id: doc.id,
          ...data,
        } as Notification)
      })

      return notifications
    } catch (error) {
      console.error("Error getting notifications:", error)
      throw error
    }
  }

  /**
   * Get unread notification count for a user
   * @param userId User ID
   * @returns Number of unread notifications
   */
  static async getUnreadCount(userId: string): Promise<number> {
    try {
      const q = query(
        collection(db, this.COLLECTION, userId, this.SUBCOLLECTION),
        where("read", "==", false)
      )

      const snapshot = await getDocs(q)
      return snapshot.size
    } catch (error) {
      console.error("Error getting unread notification count:", error)
      throw error
    }
  }

  /**
   * Mark a notification as read
   * @param userId User ID
   * @param notificationId Notification ID
   * @returns Service response
   */
  static async markAsRead(userId: string, notificationId: string): Promise<ServiceResponse> {
    try {
      const notificationRef = doc(
        db,
        this.COLLECTION,
        userId,
        this.SUBCOLLECTION,
        notificationId
      )

      await updateDoc(notificationRef, {
        read: true,
        updatedAt: serverTimestamp(),
      })

      return { success: true, id: notificationId }
    } catch (error) {
      console.error("Error marking notification as read:", error)
      return { success: false, error }
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param userId User ID
   * @returns Service response
   */
  static async markAllAsRead(userId: string): Promise<ServiceResponse> {
    try {
      const q = query(
        collection(db, this.COLLECTION, userId, this.SUBCOLLECTION),
        where("read", "==", false)
      )

      const snapshot = await getDocs(q)
      const batch = writeBatch(db)

      snapshot.forEach((doc) => {
        batch.update(doc.ref, {
          read: true,
          updatedAt: serverTimestamp(),
        })
      })

      await batch.commit()

      return { success: true }
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete a notification
   * @param userId User ID
   * @param notificationId Notification ID
   * @returns Service response
   */
  static async deleteNotification(userId: string, notificationId: string): Promise<ServiceResponse> {
    try {
      const notificationRef = doc(
        db,
        this.COLLECTION,
        userId,
        this.SUBCOLLECTION,
        notificationId
      )
      await deleteDoc(notificationRef)

      return { success: true, id: notificationId }
    } catch (error) {
      console.error("Error deleting notification:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete old notifications (older than 30 days)
   * @param userId User ID
   * @returns Service response
   */
  static async deleteOldNotifications(userId: string): Promise<ServiceResponse> {
    try {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const q = query(
        collection(db, this.COLLECTION, userId, this.SUBCOLLECTION),
        where("createdAt", "<", thirtyDaysAgo)
      )

      const snapshot = await getDocs(q)
      const batch = writeBatch(db)

      snapshot.forEach((doc) => {
        batch.delete(doc.ref)
      })

      await batch.commit()

      return { success: true }
    } catch (error) {
      console.error("Error deleting old notifications:", error)
      return { success: false, error }
    }
  }
}
