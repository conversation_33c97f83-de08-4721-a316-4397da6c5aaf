"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useSyncTripAttendees } from "@/lib/domains/user-trip/user-trip.hooks"
import { useRealtimeTripAttendeesWithDetails } from "@/lib/domains/user-trip/user-trip.realtime.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { RefreshCw, CheckCircle, AlertCircle } from "lucide-react"

interface TripAttendeesDebugProps {
  tripId: string
  tripAttendees: string[] // From trip.attendees
}

/**
 * Debug component to help identify and fix trip attendees synchronization issues
 * This component shows the difference between trip.attendees and actual user trip statuses
 */
export function TripAttendeesDebug({ tripId, tripAttendees }: TripAttendeesDebugProps) {
  const user = useUser()
  const { syncAttendees, syncing } = useSyncTripAttendees()
  const { attendeesWithDetails, loading } = useRealtimeTripAttendeesWithDetails(tripId)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)

  // Extract actual attendees from user trip statuses
  const actualAttendees = attendeesWithDetails.map((a) => a.userId)

  // Check if current user is in trip attendees (for permission debugging)
  const userInTripAttendees = user?.uid ? tripAttendees.includes(user.uid) : false
  const userActuallyAttending = user?.uid ? actualAttendees.includes(user.uid) : false

  // Find mismatches
  const missingFromTrip = actualAttendees.filter((uid) => !tripAttendees.includes(uid))
  const extraInTrip = tripAttendees.filter((uid) => !actualAttendees.includes(uid))

  const hasMismatches = missingFromTrip.length > 0 || extraInTrip.length > 0

  const handleSync = async () => {
    const success = await syncAttendees(tripId)
    if (success) {
      setLastSyncTime(new Date())
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-4">
          <p className="text-sm text-muted-foreground">Loading attendees data...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm flex items-center gap-2">
          Trip Attendees Debug
          {hasMismatches ? (
            <AlertCircle className="h-4 w-4 text-orange-500" />
          ) : (
            <CheckCircle className="h-4 w-4 text-green-500" />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current User Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Current User Status</h4>
          <div className="flex gap-2">
            <Badge variant={userInTripAttendees ? "default" : "destructive"}>
              In trip.attendees: {userInTripAttendees ? "Yes" : "No"}
            </Badge>
            <Badge variant={userActuallyAttending ? "default" : "secondary"}>
              Actually attending: {userActuallyAttending ? "Yes" : "No"}
            </Badge>
          </div>
          {!userInTripAttendees && userActuallyAttending && (
            <p className="text-xs text-orange-600">
              ⚠️ You're attending but not in trip.attendees - this will cause permission errors!
            </p>
          )}
        </div>

        {/* Attendees Comparison */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Attendees Comparison</h4>
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <p className="font-medium">Trip.attendees ({tripAttendees.length})</p>
              <div className="space-y-1">
                {tripAttendees.map((uid) => (
                  <div key={uid} className="truncate">
                    {uid}
                  </div>
                ))}
              </div>
            </div>
            <div>
              <p className="font-medium">Actually attending ({actualAttendees.length})</p>
              <div className="space-y-1">
                {actualAttendees.map((uid) => (
                  <div key={uid} className="truncate">
                    {uid}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Mismatches */}
        {hasMismatches && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-orange-600">Mismatches Found</h4>
            {missingFromTrip.length > 0 && (
              <div>
                <p className="text-xs font-medium">Missing from trip.attendees:</p>
                <div className="text-xs space-y-1">
                  {missingFromTrip.map((uid) => (
                    <div key={uid} className="truncate text-orange-600">
                      {uid}
                    </div>
                  ))}
                </div>
              </div>
            )}
            {extraInTrip.length > 0 && (
              <div>
                <p className="text-xs font-medium">Extra in trip.attendees:</p>
                <div className="text-xs space-y-1">
                  {extraInTrip.map((uid) => (
                    <div key={uid} className="truncate text-blue-600">
                      {uid}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Sync Button */}
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant={hasMismatches ? "default" : "outline"}
            onClick={handleSync}
            disabled={syncing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${syncing ? "animate-spin" : ""}`} />
            Sync Attendees
          </Button>
          {lastSyncTime && (
            <p className="text-xs text-muted-foreground">
              Last synced: {lastSyncTime.toLocaleTimeString()}
            </p>
          )}
        </div>

        {!hasMismatches && (
          <p className="text-xs text-green-600">✅ Attendees are properly synchronized</p>
        )}
      </CardContent>
    </Card>
  )
}
