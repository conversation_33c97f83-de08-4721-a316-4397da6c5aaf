"use client"

import { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Send } from "lucide-react"
import { UserMention } from "./user-mention"
import { useSendMessage } from "@/lib/domains/message/message.hooks"
import { useCreateMessageMentionNotifications } from "@/lib/domains/notification/notification.hooks"
import { MessageCreateData } from "@/lib/domains/message/message.types"

interface MessageInputProps {
  tripId: string
  tripName: string
  attendees: Array<{
    id: string
    displayName: string
    photoURL?: string
  }>
  currentUser: {
    id: string
    displayName: string
    photoURL?: string
  }
}

export function MessageInput({ tripId, tripName, attendees, currentUser }: MessageInputProps) {
  const [content, setContent] = useState("")
  const [showMentions, setShowMentions] = useState(false)
  const [mentionQuery, setMentionQuery] = useState("")
  const [mentionPosition, setMentionPosition] = useState(0)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const { sendMessage, sending } = useSendMessage(tripId)
  const { createNotifications } = useCreateMessageMentionNotifications()

  const characterLimit = 750
  const remainingChars = characterLimit - content.length

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    if (value.length <= characterLimit) {
      setContent(value)

      // Check for @ mentions
      const cursorPosition = e.target.selectionStart
      const textBeforeCursor = value.substring(0, cursorPosition)
      const lastAtIndex = textBeforeCursor.lastIndexOf("@")

      if (lastAtIndex !== -1) {
        const textAfterAt = textBeforeCursor.substring(lastAtIndex + 1)
        // Check if there's no space after @ (still typing mention)
        if (!textAfterAt.includes(" ") && !textAfterAt.includes("\n")) {
          setMentionQuery(textAfterAt)
          setMentionPosition(lastAtIndex)
          setShowMentions(true)
        } else {
          setShowMentions(false)
        }
      } else {
        setShowMentions(false)
      }
    }
  }

  const handleMentionSelect = useCallback(
    (attendee: { id: string; displayName: string }) => {
      const beforeMention = content.substring(0, mentionPosition)
      const afterMention = content.substring(mentionPosition + mentionQuery.length + 1) // +1 for @
      const mentionText = `@[${attendee.id}:${attendee.displayName}]`

      const newContent = beforeMention + mentionText + afterMention
      setContent(newContent)
      setShowMentions(false)
      setMentionQuery("")

      // Focus back to textarea
      if (textareaRef.current) {
        textareaRef.current.focus()
        // Set cursor position after the mention
        const newCursorPosition = beforeMention.length + mentionText.length
        setTimeout(() => {
          textareaRef.current?.setSelectionRange(newCursorPosition, newCursorPosition)
        }, 0)
      }
    },
    [content, mentionPosition, mentionQuery]
  )

  const extractMentionedUserIds = (messageContent: string): string[] => {
    const mentionRegex = /@\[([^:]+):[^\]]+\]/g
    const mentions: string[] = []
    let match

    while ((match = mentionRegex.exec(messageContent)) !== null) {
      mentions.push(match[1])
    }

    return [...new Set(mentions)] // Remove duplicates
  }

  const handleSend = async () => {
    if (!content.trim() || sending) return

    try {
      // Extract mentioned user IDs
      const mentionedUserIds = extractMentionedUserIds(content)

      // Prepare message data - only include senderPhotoURL if it exists
      const messageData: MessageCreateData = {
        tripId,
        senderId: currentUser.id,
        senderName: currentUser.displayName,
        content: content.trim(),
        mentionedUserIds,
        ...(currentUser.photoURL && { senderPhotoURL: currentUser.photoURL }),
      }

      // Send the message
      await sendMessage(messageData)

      // Create notifications for mentioned users
      if (mentionedUserIds.length > 0) {
        // Get trip name - we'll need to pass this from parent or fetch it
        // For now, using a placeholder
        await createNotifications(
          mentionedUserIds,
          tripId,
          tripName,
          currentUser.displayName,
          currentUser.id,
          currentUser.photoURL || undefined
        )
      }

      // Clear the input
      setContent("")
      setShowMentions(false)
      setMentionQuery("")
    } catch (error) {
      console.error("Error sending message:", error)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }

    if (e.key === "Escape") {
      setShowMentions(false)
    }
  }

  const filteredAttendees = attendees.filter(
    (attendee) =>
      attendee.displayName.toLowerCase().includes(mentionQuery.toLowerCase()) &&
      attendee.id !== currentUser.id // Don't show current user in mentions
  )

  return (
    <div className="relative">
      {/* User Mention Dropdown */}
      {showMentions && filteredAttendees.length > 0 && (
        <div className="absolute bottom-full left-0 right-0 mb-2">
          <UserMention
            attendees={filteredAttendees}
            onSelect={handleMentionSelect}
            query={mentionQuery}
          />
        </div>
      )}

      <div className="flex gap-2 items-end">
        <div className="flex-1">
          <Textarea
            ref={textareaRef}
            value={content}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type a message... Use @ to mention someone"
            className="min-h-[60px] max-h-[120px] resize-none"
            disabled={sending}
          />
          <div className="flex justify-between items-center mt-1">
            <span
              className={`text-xs ${remainingChars < 50 ? "text-destructive" : "text-muted-foreground"}`}
            >
              {remainingChars} characters remaining
            </span>
            {showMentions && (
              <span className="text-xs text-muted-foreground">Press Esc to close mentions</span>
            )}
          </div>
        </div>

        <Button
          onClick={handleSend}
          disabled={!content.trim() || sending}
          size="icon"
          className="shrink-0"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
