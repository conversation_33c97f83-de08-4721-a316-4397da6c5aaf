"use client"

import { useEffect, useRef } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { Message } from "@/lib/domains/message/message.types"
import { useLoadMoreMessages } from "@/lib/domains/message/message.hooks"

interface MessageListProps {
  messages: Message[]
  currentUserId: string
  attendees: Array<{
    id: string
    displayName: string
    photoURL?: string
  }>
  tripId: string
  messagesContainerRef: React.RefObject<HTMLDivElement>
}

export function MessageList({
  messages,
  currentUserId,
  attendees,
  tripId,
  messagesContainerRef,
}: MessageListProps) {
  const { loadMore, loading: loadingMore, hasMore } = useLoadMoreMessages(tripId)

  const loadMoreTriggered = useRef(false)

  // Handle scroll to detect when user is near top for infinite scroll
  useEffect(() => {
    const container = messagesContainerRef.current
    if (!container) return

    const handleScroll = () => {
      const { scrollTop } = container
      const nearTop = scrollTop < 100

      // Trigger load more when near top and not already loading
      if (nearTop && hasMore && !loadingMore && !loadMoreTriggered.current) {
        loadMoreTriggered.current = true
        loadMore().finally(() => {
          loadMoreTriggered.current = false
        })
      }
    }

    container.addEventListener("scroll", handleScroll)
    return () => container.removeEventListener("scroll", handleScroll)
  }, [hasMore, loadingMore, loadMore, messagesContainerRef])

  const formatMessageTime = (createdAt: any) => {
    if (!createdAt) return ""

    try {
      const date = createdAt.toDate ? createdAt.toDate() : new Date(createdAt)
      return formatDistanceToNow(date, { addSuffix: true })
    } catch (error) {
      return ""
    }
  }

  const renderMessageContent = (content: string) => {
    // Replace @[userId:displayName] with styled mentions
    const mentionRegex = /@\[([^:]+):([^\]]+)\]/g
    const parts = content.split(mentionRegex)

    const result = []
    for (let i = 0; i < parts.length; i += 3) {
      // Regular text
      if (parts[i]) {
        result.push(parts[i])
      }

      // Mention (userId at i+1, displayName at i+2)
      if (parts[i + 1] && parts[i + 2]) {
        const userId = parts[i + 1]
        const displayName = parts[i + 2]
        const isCurrentUser = userId === currentUserId

        result.push(
          <span
            key={`mention-${i}`}
            className={`inline-block px-1 py-0.5 rounded text-sm font-medium ${
              isCurrentUser ? "bg-primary/20 text-primary" : "bg-muted text-muted-foreground"
            }`}
          >
            @{displayName}
          </span>
        )
      }
    }

    return result
  }

  const getAttendeeInfo = (senderId: string) => {
    return attendees.find((attendee) => attendee.id === senderId)
  }

  return (
    <div className="space-y-4">
      {/* Load More Button */}
      {hasMore && (
        <div className="text-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={loadMore}
            disabled={loadingMore}
            className="text-muted-foreground"
          >
            {loadingMore ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Loading more messages...
              </>
            ) : (
              "Load more messages"
            )}
          </Button>
        </div>
      )}

      {/* Messages */}
      {messages.map((message, index) => {
        const isOwnMessage = message.senderId === currentUserId
        const attendeeInfo = getAttendeeInfo(message.senderId)
        const showAvatar = !isOwnMessage
        const showName =
          !isOwnMessage && (index === 0 || messages[index - 1]?.senderId !== message.senderId)

        return (
          <div
            key={message.id}
            className={`flex gap-3 ${isOwnMessage ? "justify-end" : "justify-start"}`}
          >
            {/* Avatar (only for others' messages) */}
            {showAvatar && (
              <Avatar className="h-8 w-8 shrink-0">
                <AvatarImage
                  src={attendeeInfo?.photoURL || message.senderPhotoURL}
                  alt={attendeeInfo?.displayName || message.senderName}
                />
                <AvatarFallback>
                  {(attendeeInfo?.displayName || message.senderName)?.charAt(0) || "U"}
                </AvatarFallback>
              </Avatar>
            )}

            {/* Message Content */}
            <div
              className={`flex flex-col max-w-[70%] ${isOwnMessage ? "items-end" : "items-start"}`}
            >
              {/* Sender Name and Time */}
              {showName && (
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm font-medium">
                    {attendeeInfo?.displayName || message.senderName}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {formatMessageTime(message.createdAt)}
                  </span>
                </div>
              )}

              {/* Message Bubble */}
              <div
                className={`px-3 py-2 rounded-lg max-w-full break-words ${
                  isOwnMessage ? "bg-primary text-primary-foreground" : "bg-muted"
                }`}
              >
                <div className="text-sm whitespace-pre-wrap">
                  {renderMessageContent(message.content)}
                </div>
              </div>

              {/* Time for own messages */}
              {isOwnMessage && (
                <span className="text-xs text-muted-foreground mt-1">
                  {formatMessageTime(message.createdAt)}
                </span>
              )}
            </div>

            {/* Spacer for own messages to align with avatar space */}
            {isOwnMessage && <div className="w-8 shrink-0" />}
          </div>
        )
      })}
    </div>
  )
}
