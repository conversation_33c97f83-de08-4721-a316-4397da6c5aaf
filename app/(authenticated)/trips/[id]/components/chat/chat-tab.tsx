"use client"

import { useRef, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { MessageList } from "./message-list"
import { MessageInput } from "./message-input"
import { Button } from "@/components/ui/button"
import { ArrowDown } from "lucide-react"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import {
  useRealtimeMessages,
  useRealtimeNewMessages,
  useNewMessageNotifications,
  useMessageScroll,
} from "@/lib/domains/message/message.hooks"
import { Trip } from "@/lib/domains/trip/trip.types"

interface ChatTabProps {
  trip: Trip
  attendees: Array<{
    id: string
    displayName?: string
    email?: string
    photoURL?: string
  }>
  isActive?: boolean
}

export function ChatTab({ trip, attendees, isActive = true }: ChatTabProps) {
  const { user } = useAuthStatus()
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // Subscribe to real-time messages (only when active)
  const { messages, loading, error } = useRealtimeMessages(isActive ? trip.id : "", true)

  // Subscribe to new message notifications (only when active)
  useRealtimeNewMessages(isActive ? trip.id : "", user?.uid || "", true)

  // Get new message count and scroll functions
  const { newMessageCount, resetCount } = useNewMessageNotifications(trip.id)
  const { scrollToBottom, isAtBottom } = useMessageScroll(trip.id, messagesContainerRef)

  // Auto-scroll to bottom on initial load
  useEffect(() => {
    if (messages.length > 0 && !loading) {
      scrollToBottom()
    }
  }, [messages.length, loading, scrollToBottom])

  const handleScrollToBottom = () => {
    scrollToBottom()
    resetCount()
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <p>Failed to load messages. Please try again.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card className="h-[600px] flex flex-col">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Trip Chat</CardTitle>
          <p className="text-sm text-muted-foreground">
            Chat with your trip attendees. Use @ to mention someone.
          </p>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0 relative">
          {/* Messages Container */}
          <div ref={messagesContainerRef} className="flex-1 overflow-y-auto p-4 space-y-4">
            {loading && messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">
                  <p>Loading messages...</p>
                </div>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">
                  <p>No messages yet. Start the conversation!</p>
                </div>
              </div>
            ) : (
              <MessageList
                messages={messages}
                currentUserId={user?.uid || ""}
                attendees={attendees.map((attendee) => ({
                  id: attendee.id,
                  displayName: attendee.displayName || attendee.email || "Unknown",
                  photoURL: attendee.photoURL,
                }))}
                tripId={trip.id}
                messagesContainerRef={messagesContainerRef}
              />
            )}
          </div>

          {/* New Message Indicator */}
          {newMessageCount > 0 && (
            <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10">
              <Button
                variant="secondary"
                size="sm"
                onClick={handleScrollToBottom}
                className="shadow-lg"
              >
                <ArrowDown className="h-4 w-4 mr-2" />
                {newMessageCount} new message{newMessageCount > 1 ? "s" : ""}
              </Button>
            </div>
          )}

          {/* Message Input */}
          <div className="border-t p-4">
            <MessageInput
              tripId={trip.id}
              tripName={trip.name}
              attendees={attendees.map((attendee) => ({
                id: attendee.id,
                displayName: attendee.displayName || attendee.email || "Unknown",
                photoURL: attendee.photoURL,
              }))}
              currentUser={{
                id: user?.uid || "",
                displayName: user?.displayName || "",
                photoURL: user?.photoURL || undefined,
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
