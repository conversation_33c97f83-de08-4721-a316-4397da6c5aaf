"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent } from "@/components/ui/card"

interface UserMentionProps {
  attendees: Array<{
    id: string
    displayName: string
    photoURL?: string
  }>
  onSelect: (attendee: { id: string; displayName: string }) => void
  query: string
}

export function UserMention({ attendees, onSelect, query }: UserMentionProps) {
  const handleSelect = (attendee: { id: string; displayName: string }) => {
    onSelect(attendee)
  }

  const highlightMatch = (text: string, query: string) => {
    if (!query) return text
    
    const index = text.toLowerCase().indexOf(query.toLowerCase())
    if (index === -1) return text
    
    return (
      <>
        {text.substring(0, index)}
        <span className="bg-primary/20 font-medium">
          {text.substring(index, index + query.length)}
        </span>
        {text.substring(index + query.length)}
      </>
    )
  }

  return (
    <Card className="shadow-lg border">
      <CardContent className="p-2">
        <div className="space-y-1 max-h-48 overflow-y-auto">
          {attendees.length === 0 ? (
            <div className="p-2 text-sm text-muted-foreground text-center">
              No attendees found
            </div>
          ) : (
            attendees.map((attendee) => (
              <div
                key={attendee.id}
                onClick={() => handleSelect(attendee)}
                className="flex items-center gap-2 p-2 rounded-md hover:bg-muted cursor-pointer transition-colors"
              >
                <Avatar className="h-6 w-6">
                  <AvatarImage src={attendee.photoURL} alt={attendee.displayName} />
                  <AvatarFallback className="text-xs">
                    {attendee.displayName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm">
                  {highlightMatch(attendee.displayName, query)}
                </span>
              </div>
            ))
          )}
        </div>
        {attendees.length > 0 && (
          <div className="text-xs text-muted-foreground mt-2 px-2">
            Click to mention or continue typing
          </div>
        )}
      </CardContent>
    </Card>
  )
}
